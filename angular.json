{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "cli": {"packageManager": "npm", "schematicCollections": ["@angular-eslint/schematics"], "analytics": false}, "newProjectRoot": "projects", "projects": {"se-aviacio-mf": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"customWebpackConfig": {"path": "./extra-webpack.config.js"}, "outputPath": "dist/se-aviacio-mf", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/bootstrap/scss/bootstrap.scss", "./node_modules/se-ui-components-mf-lib/src/lib/styles.scss", "src/styles.scss"], "scripts": ["node_modules/@webcomponents/custom-elements/src/native-shim.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.production.ts"}], "optimization": true, "outputHashing": "none", "sourceMap": false, "namedChunks": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"loc": {"browserTarget": "se-aviacio-mf:build:loc", "proxyConfig": "proxy.conf.loc.json"}, "dev": {"browserTarget": "se-aviacio-mf:build:development", "proxyConfig": "proxy.conf.dev.json"}, "int": {"browserTarget": "se-aviacio-mf:build:int", "proxyConfig": "proxy.conf.int.json"}, "production": {"browserTarget": "se-aviacio-mf:build:production"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "se-aviacio-mf:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}}