import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AppRoutes } from '@core/models';

const routes: Routes = [
  {
    path: '',
    redirectTo: AppRoutes.PARTICIPANTS,
    pathMatch: 'full',
  },
  {
    path: AppRoutes.ROOT,
    redirectTo: AppRoutes.PARTICIPANTS,
    pathMatch: 'full',
  },
  {
    path: AppRoutes.PARTICIPANTS,
    loadChildren: () =>
      import(`./modules/participants/participants.module`).then(
        (module) => module.ParticipantsModule,
      ),
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
