import { Nullable } from 'se-ui-components-mf-lib';

export enum SelfAssessmentStatusEnum {
  IN_PROGRESS = 'IN_PROGRESS',
  PENDENT_PAGAMENT = 'PENDENT_PAGAMENT',
  PRESENTAT = 'PRESENTAT',
  PAGAT = 'PAGAT',
}

export type SelfAssessmentStatus = `${SelfAssessmentStatusEnum}`;

interface SelfAssessmentStatusTranslations {
  STATE: {
    IN_PROGRESS: string;
    PRESENTAT: string;
    PAGAT: string;
    PENDENT_PAGAMENT: string;
  };
}

export interface HeaderTagData {
  translations: SelfAssessmentStatusTranslations;
  presentationDate: Nullable<string>;
  status: SelfAssessmentStatus;
}
