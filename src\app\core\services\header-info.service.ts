import { Inject, Injectable, LOCALE_ID, type OnD<PERSON>roy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, combineLatest, Subject, takeUntil } from 'rxjs';
import {
  SeTagThemeEnum,
  type Nullable,
  type SeHeaderInfoItem,
  type TagProps,
} from 'se-ui-components-mf-lib';
import { formatDate } from '@angular/common';

import { StoreService } from './store.service';
import {
  HeaderTagData,
  SelfAssessmentStatus,
  SelfAssessmentStatusEnum,
} from '../models';

@Injectable({
  providedIn: 'root',
})
export class HeaderInfoService implements OnDestroy {
  private readonly INFO_ITEMS_INITIAL_VALUE = [
    null, // Nombre del sujeto pasivo
    null, // modelo
    null, // siempre null (vacío)
    null, // tipo de trámite
    null, // Ejer<PERSON>cio
    null, // núm. justificante complementaria
  ];
  private readonly infoItemsSubject = new BehaviorSubject<
    Nullable<SeHeaderInfoItem>[]
  >(this.INFO_ITEMS_INITIAL_VALUE);
  public readonly infoItems$ = this.infoItemsSubject.asObservable();

  private readonly defaultTags = [null, null];
  private readonly tagsSubject = new BehaviorSubject<
    Nullable<Nullable<TagProps>[]>
  >(this.defaultTags);
  public readonly tags$ = this.tagsSubject.asObservable();

  private presentationDateSubject = new BehaviorSubject<Nullable<string>>(null);
  private translations$ = this.translate.get('SE_AVIACIO_MF.HEADER_INFO');

  private readonly taxYearSubject = new BehaviorSubject<Nullable<number>>(null);

  private destroyed$ = new Subject<void>();
  private _receiptId: Nullable<string>;

  taxYear$ = this.taxYearSubject.asObservable();

  constructor(
    private translate: TranslateService,
    @Inject(LOCALE_ID) private locale: string,
    private store: StoreService,
  ) {
    this.getStoreData();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.unsubscribe();
  }

  set taxpayerName(name: Nullable<string>) {
    this.translations$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((translations) => {
        this.spliceItems(0, 1, {
          term: translations['TAXPAYER'],
          details: name,
        });
      });
  }

  get taxYear(): Nullable<number> {
    return this.taxYearSubject.getValue();
  }
  set taxYear(year: Nullable<number>) {
    this.translations$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((translations) => {
        const modalityInfoItem = year
          ? { term: translations['EXERCICI'], details: String(year) }
          : null;
        this.spliceItems(3, 1, modalityInfoItem);
      });
  }

  set model(model: Nullable<string>) {
    this.translations$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((translations) => {
        const modelInfoItem = model
          ? { term: translations['MODEL'], details: model }
          : null;
        this.spliceItems(1, 1, modelInfoItem);
      });
  }

  set status(status: Nullable<SelfAssessmentStatus>) {
    combineLatest([this.translations$, this.presentationDateSubject])
      .pipe(takeUntil(this.destroyed$))
      .subscribe(([translations, presentationDate]) => {
        if (!status) {
          this.tagsSubject.next([]);
          return;
        }

        this.tagsSubject.next(
          this.createSelfAssessmentTags({
            translations,
            presentationDate,
            status,
          }),
        );
      });
  }

  set receiptId(id: Nullable<string>) {
    this._receiptId = id;

    if (!id) {
      this.spliceItems(5, 1, null);
      return;
    }

    this.translations$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((translations) => {
        this.spliceItems(5, 1, {
          term: translations['COMPLEMENTARY_OF'],
          details: id,
        });
      });
  }

  get receiptId(): Nullable<string> {
    return this._receiptId;
  }

  get presentationDate(): Nullable<string> {
    return this.presentationDateSubject.getValue();
  }
  set presentationDate(date: Nullable<string>) {
    this.presentationDateSubject.next(date);
    this.store.presentationDate = date;
  }

  resetYearEstablishment(): void {
    this.resetInfoItems(1, 6, null);
  }

  resetParticipants(): void {
    this.updateInfoItems(this.INFO_ITEMS_INITIAL_VALUE);
    this.resetStatus();
  }

  resetYear(): void {
    const infoItems = this.getInfoItems();
    infoItems?.splice(1, 5, null, null, null, null, null);
    this.updateInfoItems(infoItems);
    this.resetStatus();
  }

  resetCompleteSubheader(): void {
    this.resetInfoItems(0, 6, null);
  }

  private getInfoItems(): Nullable<Nullable<SeHeaderInfoItem>[]> {
    const data = this.infoItemsSubject.getValue();
    if (!data?.every((item) => item === null)) {
      return this.infoItemsSubject.getValue();
    } else {
      return this.store.header;
    }
  }

  private updateInfoItems(items: Nullable<Nullable<SeHeaderInfoItem>[]>): void {
    this.infoItemsSubject.next([...(items ?? this.INFO_ITEMS_INITIAL_VALUE)]);
    this.store.header = [...(items ?? this.INFO_ITEMS_INITIAL_VALUE)];
    this.store.presentedAl = false;
  }

  private resetStatus(): void {
    this.tagsSubject.next([...this.defaultTags]);
  }

  private resetInfoItems(
    start: number,
    end: number,
    fill: SeHeaderInfoItem | null,
  ): void {
    const newItems = this.infoItemsSubject.getValue();
    if (newItems) {
      newItems.fill(fill, start, end);
      this.infoItemsSubject.next([...newItems]);
      this.tagsSubject.next([]);
      this.presentationDateSubject.next(null);
    }
  }

  private spliceItems(
    start: number,
    deleteCount: number,
    ...items: Nullable<SeHeaderInfoItem>[]
  ): void {
    const newItems = this.infoItemsSubject.getValue();
    if (newItems) {
      newItems.splice(start, deleteCount, ...items);
      this.infoItemsSubject.next([...newItems]);
      this.store.header = [...newItems];
    }
  }

  private createSelfAssessmentTags({
    translations,
    presentationDate,
    status,
  }: HeaderTagData): TagProps[] {
    switch (status) {
      case SelfAssessmentStatusEnum.IN_PROGRESS: {
        return [
          {
            label: translations?.STATE.IN_PROGRESS,
            tagTheme: SeTagThemeEnum.WARNING,
          },
        ];
      }

      case SelfAssessmentStatusEnum.PAGAT: {
        return [
          {
            label: translations?.STATE.PAGAT,
            tagTheme: SeTagThemeEnum.SUCCESS,
          },
        ];
      }

      case SelfAssessmentStatusEnum.PRESENTAT: // ↓ sigue otro caso ↓
      case SelfAssessmentStatusEnum.PENDENT_PAGAMENT: {
        let label = translations?.STATE.PRESENTAT;
        if (presentationDate) {
          const date = formatDate(presentationDate, 'dd/MM/yyyy', this.locale);
          label += `: ${date}`;
        }
        const tags = [{ label, tagTheme: SeTagThemeEnum.SUCCESS }];
        if (status === SelfAssessmentStatusEnum.PENDENT_PAGAMENT) {
          tags.push({
            label: translations?.STATE.PENDENT_PAGAMENT,
            tagTheme: SeTagThemeEnum.WARNING,
          });
        }
        return tags;
      }

      default: {
        return [];
      }
    }
  }

  private getStoreData(): void {
    if (this.store.taxYear) this.taxYearSubject.next(this.store.taxYear);
  }
}
