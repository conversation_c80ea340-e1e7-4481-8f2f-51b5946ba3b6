import { Injectable } from '@angular/core';
import {
  Nullable,
  SeDataStorageService,
  SeHeaderInfoItem,
} from 'se-ui-components-mf-lib';
import { TaxPayer } from 'src/app/modules/participants/models';

/* Storage keys */
const HEADER = 'IENA_HEADER';
const ID_TRAMIT = 'IENA_ID_TRAMIT';
const SELECTED_TAXPAYER = 'IENA_SELECTED_TAXPAYER';
const TAX_YEAR = 'IENA_TAX_YEAR';
const SELF_ASSESSMENT_ID = 'IENA_SELF_ASSESSMENT_ID';
const MODEL = 'IENA_MODEL';
const WORKING_SESSION_HAS_APPEARED = 'IENA_WORKING_SESSION_HAS_APPEARED';
const RESPONSIBLE_DECLARATION_CHECKED = 'IENA_RESPONSIBLE_DECLARATION_CHECKED';
const AMOUNT_TO_PAY = 'IGEC_AMOUNT_TO_PAY';
const DECLARED = 'IENA_DECLARED';
const PRESENTATION_DATE = 'IENA_PRESENTATION_DATE';
const PRESENTED_AL = 'IENA_PRESENTED_AL';
const PAYMENT_ID = 'IENA_PAYMENT_ID';

@Injectable({
  providedIn: 'root',
})
export class StoreService {
  constructor(private storage: SeDataStorageService) {
    // Intencionadamente vacío
  }

  get header(): Nullable<SeHeaderInfoItem>[] {
    return this.storage.getItem(HEADER);
  }
  set header(header: Nullable<SeHeaderInfoItem>[]) {
    this.storage.setItem(HEADER, header);
  }

  get idTramit(): Nullable<string> {
    return this.storage.getItem(ID_TRAMIT);
  }
  set idTramit(idTramit: Nullable<string>) {
    this.storage.setItem(ID_TRAMIT, idTramit);
  }

  get hasAppearedWorkingSession(): Nullable<boolean> {
    return this.storage.getItem(WORKING_SESSION_HAS_APPEARED);
  }
  set hasAppearedWorkingSession(hasAppeared: Nullable<boolean>) {
    this.storage.setItem(WORKING_SESSION_HAS_APPEARED, hasAppeared);
  }

  get selectedTaxpayer(): Nullable<TaxPayer> {
    return this.storage.getItem(SELECTED_TAXPAYER);
  }
  set selectedTaxpayer(taxpayer: Nullable<TaxPayer>) {
    this.storage.setItem(SELECTED_TAXPAYER, taxpayer);
  }

  get declared(): Nullable<boolean> {
    return this.storage.getItem(DECLARED);
  }
  set declared(declared: Nullable<boolean>) {
    this.storage.setItem(DECLARED, declared);
  }

  get responsibleDeclaration(): Nullable<boolean> {
    return this.storage.getItem(RESPONSIBLE_DECLARATION_CHECKED);
  }
  set responsibleDeclaration(check: Nullable<boolean>) {
    this.storage.setItem(RESPONSIBLE_DECLARATION_CHECKED, check);
  }

  get taxYear(): Nullable<number> {
    return this.storage.getItem(TAX_YEAR);
  }
  set taxYear(taxYear: Nullable<number>) {
    this.storage.setItem(TAX_YEAR, taxYear);
  }

  get selfAssessmentId(): Nullable<string> {
    return this.storage.getItem(SELF_ASSESSMENT_ID);
  }
  set selfAssessmentId(selfassessmentId: Nullable<string>) {
    this.storage.setItem(SELF_ASSESSMENT_ID, selfassessmentId);
  }

  get amountToPay(): Nullable<number> {
    return this.storage.getItem(AMOUNT_TO_PAY);
  }
  set amountToPay(amount: Nullable<number>) {
    this.storage.setItem(AMOUNT_TO_PAY, amount);
  }

  get model(): Nullable<string> {
    return this.storage.getItem(MODEL);
  }
  set model(model: Nullable<string>) {
    this.storage.setItem(MODEL, model);
  }

  get presentationDate(): Nullable<string> {
    return this.storage.getItem(PRESENTATION_DATE);
  }
  set presentationDate(presentationDate: Nullable<string>) {
    this.storage.setItem(PRESENTATION_DATE, presentationDate);
  }

  get presentedAl(): Nullable<boolean> {
    return this.storage.getItem(PRESENTED_AL);
  }
  set presentedAl(presented: Nullable<boolean>) {
    this.storage.setItem(PRESENTED_AL, presented);
  }

  get paymentId(): Nullable<string> {
    return this.storage.getItem(PAYMENT_ID);
  }
  set paymentId(id: Nullable<string>) {
    this.storage.setItem(PAYMENT_ID, id);
  }
}
