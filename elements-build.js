const fs = require('fs-extra');  
const concat = require('concat');
var pjson = require('./package.json');

(async function build() {
    // Reset previous version
    await fs.emptyDir('./elements');

    // Concatenate Webcomponent JS files
    const files = [];

    //passsing directoryPath and callback function
    const directory = './dist/se-aviacio-mf/';

    await fs.ensureDir('./dist/se-aviacio-mf');
    const files1 = await fs.readdir(directory);
    files1.filter(file => file.match(/(\w*)\.js$/)).forEach(function (file) {
        // Do whatever you want to do with the file
        //console.log(file)
        files.push(directory + file)
    });

    await fs.ensureDir('./elements');
    await concat(files, './elements/se-aviacio.js');

    // Copy styles
    await fs.copyFile(directory + 'styles.css', './elements/styles.css');

    // Copy assets
    await fs.ensureDir('./elements/assets');
    await fs.copy(directory + 'assets', './elements/assets');

    var buildtime = new Date();
    var versionJson = "{\"build\":{\"version\":\""
        + pjson.version + "\",\"name\":\""
        + pjson.name + "\",\"time\":\""
        + buildtime.toISOString() + "\"}}";
    fs.writeFileSync('./elements/version.json', versionJson);
})();
