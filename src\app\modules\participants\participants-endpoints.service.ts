import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeHttpService } from 'se-ui-components-mf-lib';

import { environment } from 'src/environments/environment';
import {
  CreateProcedureRequest,
  PostCreateProcedureHttpResponse,
} from './models';

@Injectable({
  providedIn: 'root',
})
export class ParticipantsService {
  constructor(private httpService: SeHttpService) {
    /* intentionally empty constructor */
  }

  createProcedure(
    body: CreateProcedureRequest,
  ): Observable<PostCreateProcedureHttpResponse> {
    return this.httpService.post({
      baseUrl: environment.baseUrlTributs,
      url: '/create',
      method: 'post',
      body,
    });
  }
}
